import { AuthApiError } from '@supabase/supabase-js';
import { createClient as createBrowserClient } from '@/lib/supabase';

export interface EmailConfirmationError {
  isEmailNotConfirmed: boolean;
  email?: string;
  message: string;
  canResend: boolean;
}

/**
 * Checks if an error is related to email not being confirmed
 */
export function isEmailNotConfirmedError(error: any): boolean {
  if (!error) return false;
  
  // Check for specific error messages that indicate email not confirmed
  const errorMessage = error.message?.toLowerCase() || '';
  
  return (
    errorMessage.includes('email not confirmed') ||
    errorMessage.includes('email_not_confirmed') ||
    errorMessage.includes('confirm your email') ||
    errorMessage.includes('email confirmation required') ||
    (error instanceof AuthApiError && error.message?.includes('email not confirmed'))
  );
}

/**
 * Handles email confirmation errors and provides appropriate response
 */
export function handleEmailConfirmationError(error: any, email?: string): EmailConfirmationError {
  const isEmailError = isEmailNotConfirmedError(error);
  
  if (isEmailError) {
    return {
      isEmailNotConfirmed: true,
      email: email,
      message: 'Email Anda belum dikonfirmasi. Silakan periksa email Anda dan klik link konfirmasi, atau kirim ulang email konfirmasi.',
      canResend: true
    };
  }
  
  return {
    isEmailNotConfirmed: false,
    message: error?.message || 'Terjadi kesalahan saat login',
    canResend: false
  };
}

/**
 * Resends email confirmation
 */
export async function resendEmailConfirmation(email: string): Promise<{
  success: boolean;
  message: string;
  error?: any;
}> {
  try {
    const supabase = createBrowserClient();
    
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });
    
    if (error) {
      console.error('Error resending confirmation email:', error);
      return {
        success: false,
        message: 'Gagal mengirim ulang email konfirmasi. Silakan coba lagi.',
        error
      };
    }
    
    return {
      success: true,
      message: 'Email konfirmasi telah dikirim ulang. Silakan periksa kotak masuk Anda.'
    };
  } catch (error) {
    console.error('Exception while resending confirmation email:', error);
    return {
      success: false,
      message: 'Terjadi kesalahan saat mengirim ulang email konfirmasi.',
      error
    };
  }
}

/**
 * Creates a user-friendly error message for authentication errors
 */
export function getAuthErrorMessage(error: any, email?: string): string {
  const confirmationError = handleEmailConfirmationError(error, email);
  
  if (confirmationError.isEmailNotConfirmed) {
    return confirmationError.message;
  }
  
  // Handle other common auth errors
  if (error instanceof AuthApiError) {
    const errorMessage = error.message?.toLowerCase() || '';
    
    if (errorMessage.includes('invalid login credentials') || 
        errorMessage.includes('invalid email or password')) {
      return 'Email atau kata sandi salah';
    }
    
    if (errorMessage.includes('too many requests')) {
      return 'Terlalu banyak percobaan login. Silakan coba lagi nanti.';
    }
    
    if (errorMessage.includes('user not found')) {
      return 'Akun dengan email ini tidak ditemukan';
    }
  }
  
  return error?.message || 'Terjadi kesalahan saat login';
}
