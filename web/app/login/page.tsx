'use client';

import { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { FcGoogle } from "react-icons/fc";
import NavbarMinimal from '@/components/NavbarMinimal';
import Seo from '@/components/Seo';

// Component that uses useSearchParams
function LoginForm({ onLogin, redirectPath }: { onLogin: (redirectPath: string | null) => Promise<void>; redirectPath: string | null }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [socialLoginError, setSocialLoginError] = useState<string | null>(null);
  const [isEmailNotConfirmed, setIsEmailNotConfirmed] = useState(false);
  const [isResendingConfirmation, setIsResendingConfirmation] = useState(false);
  const [resendSuccess, setResendSuccess] = useState<string | null>(null);
  const { signIn, signInWithGoogle, resendEmailConfirmation } = useAuth();
  const router = useRouter();
  
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setError(null);
    setIsEmailNotConfirmed(false);
    setResendSuccess(null);

    try {
      const result = await signIn(email, password);

      if (result.error) {
        if (result.isEmailNotConfirmed) {
          setIsEmailNotConfirmed(true);
          setError(result.message || 'Email belum dikonfirmasi');
          return;
        }

        if (result.message) {
          setError(result.message);
          return;
        } else {
          throw result.error;
        }
      }

      await onLogin(redirectPath);
    } catch (error: any) {
      setError('Terjadi kesalahan saat login');
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleResendConfirmation = async () => {
    if (!email) {
      setError('Silakan masukkan email terlebih dahulu');
      return;
    }

    setIsResendingConfirmation(true);
    setResendSuccess(null);
    setError(null);

    try {
      const result = await resendEmailConfirmation(email);

      if (result.success) {
        setResendSuccess(result.message || 'Email konfirmasi telah dikirim ulang');
      } else {
        setError(result.message || 'Gagal mengirim ulang email konfirmasi');
      }
    } catch (error: any) {
      setError('Terjadi kesalahan saat mengirim ulang email konfirmasi');
    } finally {
      setIsResendingConfirmation(false);
    }
  };

  return (
    <form className="mt-8 space-y-6" onSubmit={handleLogin}>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative" role="alert">
          <span className="block sm:inline">{error}</span>
          {isEmailNotConfirmed && (
            <div className="mt-3">
              <button
                type="button"
                onClick={handleResendConfirmation}
                disabled={isResendingConfirmation}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResendingConfirmation ? 'Mengirim...' : 'Kirim Ulang Email Konfirmasi'}
              </button>
            </div>
          )}
        </div>
      )}

      {resendSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative" role="alert">
          <span className="block sm:inline">{resendSuccess}</span>
        </div>
      )}
      
      <div className="space-y-4">
        <div>
          <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">
            Alamat Email
          </label>
          <input
            id="email-address"
            name="email"
            type="email"
            autoComplete="email"
            required
            className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
            placeholder="Alamat Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
        </div>
      </div>

      <div className="flex items-center justify-end">
        <div className="text-sm">
          <Link href="/forgot-password" className="font-medium text-primary hover:text-primary-dark">
            Lupa password?
          </Link>
        </div>
      </div>

      <div>
        <button
          type="submit"
          disabled={isLoggingIn}
          className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
        >
          {isLoggingIn ? 'Memproses...' : 'Masuk'}
        </button>
      </div>
      
      <div className="mt-4">
        {socialLoginError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4" role="alert">
            <span className="block sm:inline">{socialLoginError}</span>
          </div>
        )}
        
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Atau masuk dengan</span>
          </div>
        </div>
        
        <div className="flex justify-center mt-4">
          <button
            type="button"
            onClick={async () => {
              setSocialLoginError(null);
              try {
                const result = await signInWithGoogle(redirectPath ? encodeURIComponent(redirectPath) : undefined);
                if (result.success && result.message) {
                  router.push(result.message);
                }
                if (result.error) {
                  if (result.message) {
                    setSocialLoginError(result.message);
                  } else {
                    throw result.error;
                  }
                }
              } catch (error: any) {
                setSocialLoginError('Gagal login dengan Google');
              }
            }}
            className="w-full py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary flex items-center justify-center"
          >
            <span className="flex items-center justify-center w-full">
              <FcGoogle size={20} />
              <span className="ml-2">Google</span>
            </span>
          </button>
        </div>
      </div>
    </form>
  );
}

function LoginPage() {
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get('redirect');

  const handleLoginSuccess = async (redirectPath: string | null) => {
    const targetPath = redirectPath || '/';
    window.location.href = targetPath;
  };
  
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Seo 
        title="Masuk ke Akun | Gigsta"
        description="Masuk ke akun Gigsta Anda untuk membuat surat lamaran, email lamaran, dan analisis kecocokan CV dengan lowongan kerja."
        canonical="https://gigsta.io/login"
      />
      {/* Navigation Bar */}
      <NavbarMinimal />
      
      {/* Login Form Container */}
      <div className="flex-grow flex items-center justify-center p-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
          <div className="text-center">
            {/* Gigsta Logo */}
            <div className="mx-auto mb-4 flex justify-center">
              <img src="/images/logo.svg" alt="Gigsta Logo" className="h-20 invert" />
            </div>
            <h2 className="text-3xl font-extrabold text-gray-900">Masuk ke Akun Anda</h2>
            <p className="mt-2 text-sm text-gray-600">
              Belum punya akun?{' '}
              <Link href={`/register${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`} className="font-medium text-primary hover:text-primary-dark">
                Daftar di sini
              </Link>
            </p>
          </div>
          
          <Suspense fallback={<div>Loading...</div>}>
            <LoginForm onLogin={handleLoginSuccess} redirectPath={redirectPath} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}

export default function Login() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginPage />
    </Suspense>
  );
}
